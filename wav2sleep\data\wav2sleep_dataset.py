"""
Wav2Sleep数据加载器，适配多信号输入格式
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from glob import glob
from typing import Dict, Tuple
import logging

# 导入信号类型常量
try:
    from ..settings import SAO2, ECG, ABD, THX, AIRFLOW
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from wav2sleep.settings import SAO2, ECG, ABD, THX, AIRFLOW

logger = logging.getLogger(__name__)


class Wav2SleepDataset(Dataset):
    """Wav2Sleep数据集，输出字典格式的信号数据"""
    
    def __init__(
        self, 
        data_folder: str,
        label_csv_path: str = './shhs1_label.csv',
        split: str = 'train',
        normalize_ahi: bool = True
    ):
        self.data_folder = data_folder
        self.split = split
        self.normalize_ahi = normalize_ahi
        
        # 加载标签数据
        self.labels_df = pd.read_csv(label_csv_path)
        logger.info(f"加载了 {len(self.labels_df)} 个标签")
        
        # 获取数据文件列表
        self.data_files = self._get_data_files()
        logger.info(f"{split} 集包含 {len(self.data_files)} 个样本")
        
        # 信号名称映射
        self.signal_names = [ECG, SAO2, ABD, THX, AIRFLOW]
        
    def _get_data_files(self):
        """获取数据文件列表"""
        pattern = os.path.join(self.data_folder, "**", "*.parquet")
        all_files = glob(pattern, recursive=True)
        
        # 简单的数据分割策略
        total_files = len(all_files)
        if self.split == 'train':
            return all_files[:int(0.8 * total_files)]
        elif self.split == 'val':
            return all_files[int(0.8 * total_files):int(0.9 * total_files)]
        else:  # test
            return all_files[int(0.9 * total_files):]
    
    def __len__(self):
        return len(self.data_files)
    
    def __getitem__(self, idx) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """
        返回:
            signals: 字典格式 {'ECG': tensor, 'SAO2': tensor, ...}
            ahi_label: AHI标签值
        """
        parquet_file = self.data_files[idx]

        try:
            # 从文件名提取session_id
            session_id = os.path.basename(parquet_file).replace('.parquet', '')

            # 加载信号数据
            signals = self._load_signals(parquet_file)

            # 获取AHI标签
            ahi_label = self._get_ahi_label(session_id)

            # 验证数据完整性
            if not self._validate_signals(signals):
                logger.warning(f"信号数据验证失败 {parquet_file}")
                return self._get_zero_signals(), torch.tensor(0.0)

            return signals, ahi_label

        except Exception as e:
            logger.error(f"加载数据失败 {parquet_file}: {e}")
            # 返回零信号和零标签作为fallback
            return self._get_zero_signals(), torch.tensor(0.0)
    
    def _load_signals(self, parquet_file: str) -> Dict[str, torch.Tensor]:
        """加载信号数据"""
        try:
            df = pd.read_parquet(parquet_file)
            signals = {}
            
            for signal_name in self.signal_names:
                if signal_name in df.columns:
                    signal_data = df[signal_name].values
                    # 处理NaN值
                    signal_data = np.nan_to_num(signal_data, nan=0.0)
                    signals[signal_name] = torch.tensor(signal_data, dtype=torch.float32)
                else:
                    # 如果信号不存在，创建零信号
                    if signal_name == ECG:
                        length = 387_789
                    else:
                        length = 96_947
                    signals[signal_name] = torch.zeros(length, dtype=torch.float32)
            
            return signals
            
        except Exception as e:
            logger.error(f"加载信号数据失败 {parquet_file}: {e}")
            return self._get_zero_signals()
    
    def _get_zero_signals(self) -> Dict[str, torch.Tensor]:
        """获取零信号（fallback）"""
        signals = {}
        for signal_name in self.signal_names:
            if signal_name == ECG:
                length = 387_789
            else:
                length = 96_947
            signals[signal_name] = torch.zeros(length, dtype=torch.float32)
        return signals

    def _validate_signals(self, signals: Dict[str, torch.Tensor]) -> bool:
        """验证信号数据的完整性"""
        try:
            # 检查是否包含所有必需的信号类型
            for signal_type in self.signal_names:
                if signal_type not in signals:
                    logger.warning(f"缺少信号类型: {signal_type}")
                    return False

                # 检查信号长度
                if signals[signal_type].shape[0] == 0:
                    logger.warning(f"信号 {signal_type} 长度为0")
                    return False

                # 检查是否包含无效值
                if torch.isnan(signals[signal_type]).any() or torch.isinf(signals[signal_type]).any():
                    logger.warning(f"信号 {signal_type} 包含NaN或Inf值")
                    return False

            return True

        except Exception as e:
            logger.error(f"验证信号时出错: {e}")
            return False
    
    def _get_ahi_label(self, session_id: str) -> torch.Tensor:
        """获取AHI标签"""
        try:
            # 从session_id中提取nsrrid，处理各种可能的格式
            # 可能的格式: "shhs1-200001", "200001", "shhs1-200001.parquet"

            # 移除文件扩展名
            clean_id = session_id.replace('.parquet', '')

            # 提取数字部分
            if 'shhs1-' in clean_id:
                nsrrid_str = clean_id.split('shhs1-')[1]
            elif 'shhs2-' in clean_id:
                nsrrid_str = clean_id.split('shhs2-')[1]
            else:
                nsrrid_str = clean_id

            # 转换为整数
            nsrrid = int(nsrrid_str)

            # 查找对应的AHI值
            row = self.labels_df[self.labels_df['nsrrid'] == nsrrid]
            if len(row) == 0:
                logger.warning(f"未找到 {session_id} (nsrrid: {nsrrid}) 的AHI标签")
                return torch.tensor(0.0)

            ahi_value = float(row['ahi_a0h3'].iloc[0])

            # 检查AHI值是否有效
            if pd.isna(ahi_value) or ahi_value < 0:
                logger.warning(f"无效的AHI值 {ahi_value} for {session_id}")
                return torch.tensor(0.0)

            # 归一化处理
            if self.normalize_ahi:
                ahi_value = self._normalize_ahi(ahi_value)

            return torch.tensor(ahi_value, dtype=torch.float32)

        except ValueError as e:
            logger.error(f"无法解析session_id {session_id}: {e}")
            return torch.tensor(0.0)
        except Exception as e:
            logger.error(f"获取AHI标签失败 {session_id}: {e}")
            return torch.tensor(0.0)
    
    def _normalize_ahi(self, ahi: float) -> float:
        """分段归一化AHI值到[0,1]"""
        if ahi < 5:
            return ahi / 20.0          # [0,5] -> [0, 0.25]
        elif ahi < 15:
            return 0.25 + (ahi-5) / 40.0   # [5,15] -> [0.25, 0.5]
        elif ahi < 30:
            return 0.5 + (ahi-15) / 60.0   # [15,30] -> [0.5, 0.75]
        else:
            return 0.75 + min((ahi-30) / 120.0, 0.25)  # [30,∞] -> [0.75, 1.0]


def create_wav2sleep_dataloader(
    data_folder: str,
    label_csv_path: str = './shhs1_label.csv',
    batch_size: int = 8,
    split: str = 'train',
    normalize_ahi: bool = True,
    num_workers: int = 4,
    shuffle: bool = None
) -> DataLoader:
    """创建Wav2Sleep数据加载器"""
    
    if shuffle is None:
        shuffle = (split == 'train')
    
    dataset = Wav2SleepDataset(
        data_folder=data_folder,
        label_csv_path=label_csv_path,
        split=split,
        normalize_ahi=normalize_ahi
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=(split == 'train')  # 训练时丢弃最后一个不完整的batch
    )
    
    return dataloader


def test_wav2sleep_dataset():
    """测试Wav2Sleep数据集"""
    print("测试Wav2Sleep数据集...")
    
    dataset = Wav2SleepDataset(
        data_folder="E:/OSA/shhs_processed",
        split='train'
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试单个样本
    if len(dataset) > 0:
        signals, ahi_label = dataset[0]
        
        print(f"信号通道: {list(signals.keys())}")
        for name, signal in signals.items():
            print(f"  {name}: shape={signal.shape}, dtype={signal.dtype}")
        
        print(f"AHI标签: {ahi_label.item():.4f}")
        
        # 测试数据加载器
        dataloader = create_wav2sleep_dataloader(
            data_folder="E:/OSA/shhs_processed",
            batch_size=2,
            split='train'
        )
        
        batch_signals, batch_ahi = next(iter(dataloader))
        print(f"\nBatch测试:")
        print(f"Batch大小: {batch_ahi.shape[0]}")
        for name, signal in batch_signals.items():
            print(f"  {name}: shape={signal.shape}")
        print(f"AHI标签: shape={batch_ahi.shape}")


if __name__ == "__main__":
    test_wav2sleep_dataset()
