"""
测试修改后的Wav2Sleep AHI回归模型
"""

import torch
import numpy as np
from wav2sleep.models.wav2sleep import Wav2Sleep, OptimalAHIRegressor
from wav2sleep.models.ahi_regression_utils import (
    calculate_ahi_metrics, 
    AdaptiveRegressionLoss,
    R2Tracker
)
from wav2sleep.data.wav2sleep_dataset import create_wav2sleep_dataloader
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_ahi_regressor():
    """测试AHI回归器"""
    print("=== 测试AHI回归器 ===")
    
    # 创建回归器
    regressor = OptimalAHIRegressor(feature_dim=128, dropout=0.2)
    
    # 测试输入
    batch_size = 4
    feature_dim = 128
    x = torch.randn(batch_size, feature_dim)
    
    # 前向传播
    output = regressor(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"输出值: {output}")
    
    assert output.shape == (batch_size,), f"期望输出形状 ({batch_size},), 实际得到 {output.shape}"
    print("✅ AHI回归器测试通过")


def test_adaptive_loss():
    """测试自适应损失函数"""
    print("\n=== 测试自适应损失函数 ===")
    
    criterion = AdaptiveRegressionLoss(alpha=0.7, beta=0.3)
    
    # 模拟预测和真实值
    pred = torch.tensor([5.2, 15.8, 25.1, 35.6])
    target = torch.tensor([5.0, 16.0, 25.0, 36.0])
    
    loss = criterion(pred, target)
    print(f"预测值: {pred}")
    print(f"真实值: {target}")
    print(f"损失值: {loss.item():.4f}")
    
    assert loss.item() > 0, "损失值应该大于0"
    print("✅ 自适应损失函数测试通过")


def test_metrics():
    """测试评估指标"""
    print("\n=== 测试评估指标 ===")
    
    # 模拟预测和真实值
    pred = torch.tensor([5.2, 15.8, 25.1, 35.6, 8.9, 12.3])
    target = torch.tensor([5.0, 16.0, 25.0, 36.0, 9.0, 12.0])
    
    metrics = calculate_ahi_metrics(pred, target)
    
    print("评估指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
    
    assert 'r2' in metrics, "应该包含R²指标"
    assert 'pearson' in metrics, "应该包含Pearson相关系数"
    assert 'mae' in metrics, "应该包含MAE指标"
    print("✅ 评估指标测试通过")


def test_r2_tracker():
    """测试R²追踪器"""
    print("\n=== 测试R²追踪器 ===")
    
    tracker = R2Tracker(window_size=10)
    
    # 模拟多个batch的数据
    for i in range(5):
        pred_batch = np.random.normal(10 + i, 2, 3)  # 模拟预测值
        target_batch = np.random.normal(10 + i, 2, 3)  # 模拟真实值
        
        tracker.update(pred_batch, target_batch)
        stats = tracker.get_stats()
        
        print(f"Batch {i+1}: R²={stats['r2']:.4f}, Pearson={stats['pearson']:.4f}, 样本数={stats['samples']}")
    
    print("✅ R²追踪器测试通过")


def test_wav2sleep_model():
    """测试完整的Wav2Sleep模型"""
    print("\n=== 测试Wav2Sleep模型 ===")
    
    try:
        # 创建模型配置
        from wav2sleep.models.wav2sleep import SignalEncoders, MultiModalAttentionEmbedder, SequenceCNN
        
        # 信号映射
        signal_map = {
            'ECG': 'ECG',
            'SAO2': 'SAO2', 
            'ABD': 'ABD',
            'THX': 'THX',
            'AIRFLOW': 'AIRFLOW'
        }
        
        # 创建组件
        signal_encoders = SignalEncoders(
            signal_map=signal_map,
            feature_dim=128,
            activation='gelu',
            norm='instance',
            size='large'
        )
        
        epoch_mixer = MultiModalAttentionEmbedder(
            feature_dim=128,
            layers=2,
            dropout=0.1,
            nhead=8
        )
        
        sequence_mixer = SequenceCNN(
            feature_dim=128,
            dropout=0.1,
            num_layers=4
        )
        
        # 创建完整模型
        model = Wav2Sleep(
            signal_encoders=signal_encoders,
            epoch_mixer=epoch_mixer,
            sequence_mixer=sequence_mixer,
            dropout=0.3
        )
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建测试输入
        batch_size = 2
        test_input = {
            'ECG': torch.randn(batch_size, 387_789),
            'SAO2': torch.randn(batch_size, 96_947),
            'ABD': torch.randn(batch_size, 96_947),
            'THX': torch.randn(batch_size, 96_947),
            'AIRFLOW': torch.randn(batch_size, 96_947)
        }
        
        # 前向传播
        output = model(test_input)
        
        print(f"输入信号形状:")
        for name, signal in test_input.items():
            print(f"  {name}: {signal.shape}")
        print(f"输出形状: {output.shape}")
        print(f"输出值: {output}")
        
        assert output.shape == (batch_size,), f"期望输出形状 ({batch_size},), 实际得到 {output.shape}"
        print("✅ Wav2Sleep模型测试通过")
        
    except Exception as e:
        print(f"❌ Wav2Sleep模型测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_data_loader():
    """测试数据加载器"""
    print("\n=== 测试数据加载器 ===")
    
    try:
        # 注意：这需要实际的数据文件
        data_folder = "E:/OSA/shhs_processed"  # 根据实际路径调整
        
        if not os.path.exists(data_folder):
            print(f"⚠️ 数据文件夹不存在: {data_folder}")
            print("跳过数据加载器测试")
            return
        
        dataloader = create_wav2sleep_dataloader(
            data_folder=data_folder,
            batch_size=2,
            split='train',
            num_workers=0
        )
        
        print(f"数据集大小: {len(dataloader.dataset)}")
        
        # 测试一个batch
        batch_signals, batch_ahi = next(iter(dataloader))
        
        print(f"Batch信号形状:")
        for name, signal in batch_signals.items():
            print(f"  {name}: {signal.shape}")
        print(f"Batch AHI形状: {batch_ahi.shape}")
        print(f"AHI值范围: [{batch_ahi.min():.4f}, {batch_ahi.max():.4f}]")
        
        print("✅ 数据加载器测试通过")
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """运行所有测试"""
    print("开始测试修改后的Wav2Sleep AHI回归模型...\n")
    
    test_ahi_regressor()
    test_adaptive_loss()
    test_metrics()
    test_r2_tracker()
    test_wav2sleep_model()
    test_data_loader()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    main()
