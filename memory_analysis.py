#!/usr/bin/env python3
"""
TimesNet显存占用分析脚本
"""

def calculate_memory_usage():
    """计算TimesNet处理大维度数据的显存占用"""
    
    print("=== TimesNet显存占用分析 ===\n")
    
    # 原始数据维度
    ecg_dim = 387_789
    other_dim = 96_947
    num_other_signals = 4  # THX, ABD, AIRFLOW, SAO2
    batch_size = 8
    
    print("1. 原始数据维度:")
    print(f"   ECG: {ecg_dim:,} 个数据点")
    print(f"   其他信号 (THX/ABD/AIRFLOW/SAO2): {other_dim:,} 个数据点 × {num_other_signals}")
    print(f"   批处理大小: {batch_size}")
    
    # 计算原始输入显存
    total_points_per_sample = ecg_dim + num_other_signals * other_dim
    total_points_batch = total_points_per_sample * batch_size
    input_memory_gb = total_points_batch * 4 / (1024**3)  # float32 = 4 bytes
    
    print(f"\n2. 原始输入显存占用:")
    print(f"   单样本总数据点: {total_points_per_sample:,}")
    print(f"   批处理总数据点: {total_points_batch:,}")
    print(f"   输入显存占用: {input_memory_gb:.2f} GB")
    
    # TimesNet中间计算显存
    print(f"\n3. TimesNet中间计算显存:")
    
    # 假设压缩到5000长度（配置文件中的seq_len）
    compressed_len = 5000
    num_features = 5  # 5个信号通道
    d_model = 256
    
    # 压缩后的输入
    compressed_input = batch_size * compressed_len * num_features * 4 / (1024**3)
    print(f"   压缩后输入 ({compressed_len}×{num_features}): {compressed_input:.4f} GB")
    
    # 嵌入层输出
    embedding_output = batch_size * compressed_len * d_model * 4 / (1024**3)
    print(f"   嵌入层输出 ({compressed_len}×{d_model}): {embedding_output:.4f} GB")
    
    # FFT计算（复数，需要2倍内存）
    fft_memory = batch_size * compressed_len * d_model * 8 / (1024**3)  # complex64 = 8 bytes
    print(f"   FFT计算内存 (复数): {fft_memory:.4f} GB")
    
    # 多层Transformer
    num_layers = 2
    attention_memory = batch_size * compressed_len * compressed_len * 4 / (1024**3)  # 注意力矩阵
    total_attention = attention_memory * num_layers
    print(f"   注意力矩阵 ({num_layers}层): {total_attention:.4f} GB")
    
    # 前馈网络
    d_ff = 1024
    ff_memory = batch_size * compressed_len * d_ff * 4 / (1024**3)
    total_ff = ff_memory * num_layers
    print(f"   前馈网络 ({num_layers}层): {total_ff:.4f} GB")
    
    # 梯度内存（反向传播时需要存储所有中间结果的梯度）
    total_forward = compressed_input + embedding_output + fft_memory + total_attention + total_ff
    gradient_memory = total_forward  # 梯度大约等于前向传播的内存
    print(f"   梯度存储: {gradient_memory:.4f} GB")
    
    # 总显存估算
    total_memory = input_memory_gb + total_forward + gradient_memory
    print(f"\n4. 总显存估算:")
    print(f"   前向传播: {total_forward:.4f} GB")
    print(f"   梯度存储: {gradient_memory:.4f} GB")
    print(f"   原始输入: {input_memory_gb:.2f} GB")
    print(f"   总计: {total_memory:.2f} GB")
    
    # 常见GPU显存对比
    print(f"\n5. GPU显存需求分析:")
    gpu_configs = [
        ("RTX 3080", 10),
        ("RTX 3090", 24),
        ("RTX 4080", 16),
        ("RTX 4090", 24),
        ("A100", 40),
        ("V100", 16)
    ]
    
    for gpu_name, gpu_memory in gpu_configs:
        if total_memory <= gpu_memory * 0.8:  # 留20%余量
            status = "✅ 可行"
        elif total_memory <= gpu_memory:
            status = "⚠️ 紧张"
        else:
            status = "❌ 不足"
        print(f"   {gpu_name} ({gpu_memory}GB): {status}")
    
    # 优化建议
    print(f"\n6. 优化建议:")
    print(f"   当前问题: 原始输入维度过大 ({input_memory_gb:.2f}GB)")
    print(f"   建议1: 预处理时进一步压缩到更小维度")
    print(f"   建议2: 使用梯度检查点减少显存")
    print(f"   建议3: 减小批处理大小")
    print(f"   建议4: 使用混合精度训练(FP16)")
    
    # 计算不同压缩比的效果
    print(f"\n7. 不同压缩策略的显存占用:")
    compression_ratios = [10, 20, 50, 100]
    for ratio in compression_ratios:
        new_ecg = ecg_dim // ratio
        new_other = other_dim // ratio
        new_total = (new_ecg + num_other_signals * new_other) * batch_size * 4 / (1024**3)
        print(f"   压缩{ratio}倍: ECG={new_ecg}, 其他={new_other}, 输入显存={new_total:.4f}GB")

if __name__ == "__main__":
    calculate_memory_usage()
