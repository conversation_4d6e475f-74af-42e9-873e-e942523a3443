import random
import torch
from torch import Tensor, nn
from ..settings import COL_MAP, HIGH_FREQ_LEN
from .ppgnet import ConvBlock1D, DilatedConvBlock
from .utils import embed_ignore_inf, get_activation
from ..settings import AHI_MAX_VALUE

CHANNEL_CONFIGS = {
    'high': {'small': [16, 16, 32, 32, 64, 64, 128, 128], 'large': [32, 32, 64, 64, 128, 128, 256, 256]},
    'low': {'small': [16, 32, 64, 64, 128, 128], 'large': [32, 64, 128, 128, 256, 256]},
}

class PolynomialFeatures(nn.Module):
    """引入二次多项式特征"""
    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x + x**2  # 添加二次项并保持维度不变

class OptimalAHIRegressor(nn.Module):
    """专门为高R²优化的AHI回归器"""

    def __init__(self, feature_dim=128, dropout=0.2):
        super().__init__()

        # 输入投影层
        self.input_proj = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.LayerNorm(256),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        # 多个残差块 - 增强特征表达能力
        self.residual_blocks = nn.ModuleList([
            self._make_residual_block(256, dropout) for _ in range(4)
        ])

        # 自注意力层 - 捕获特征间的复杂关系
        self.self_attention = nn.MultiheadAttention(
            embed_dim=256,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )

        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.LayerNorm(64),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        # 最终回归头 - 多个专家网络
        self.expert_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(64, 32),
                nn.GELU(),
                nn.Dropout(dropout/2),
                nn.Linear(32, 1)
            ) for _ in range(3)  # 3个专家网络
        ])

        # 专家权重网络
        self.expert_weights = nn.Sequential(
            nn.Linear(64, 32),
            nn.GELU(),
            nn.Linear(32, 3),
            nn.Softmax(dim=-1)
        )

    def _make_residual_block(self, dim, dropout):
        return nn.Sequential(
            nn.Linear(dim, dim),
            nn.LayerNorm(dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(dim, dim),
            nn.LayerNorm(dim)
        )

    def forward(self, x):
        # 输入投影
        x = self.input_proj(x)  # [B, 256]

        # 残差连接
        for block in self.residual_blocks:
            residual = x
            x = block(x) + residual

        # 自注意力 (需要添加序列维度)
        x_seq = x.unsqueeze(1)  # [B, 1, 256]
        attn_out, _ = self.self_attention(x_seq, x_seq, x_seq)
        x = attn_out.squeeze(1) + x  # 残差连接

        # 特征融合
        x = self.feature_fusion(x)  # [B, 64]

        # 多专家预测
        expert_outputs = []
        for expert in self.expert_heads:
            expert_outputs.append(expert(x))

        # 专家权重
        weights = self.expert_weights(x)  # [B, 3]

        # 加权融合专家预测
        expert_stack = torch.stack(expert_outputs, dim=-1)  # [B, 1, 3]
        weighted_output = torch.sum(expert_stack * weights.unsqueeze(1), dim=-1)

        return weighted_output.squeeze(-1)  # [B]


class Wav2Sleep(nn.Module):
    """AHI回归预测模型"""
    def __init__(
        self,
        signal_encoders: 'SignalEncoders',
        epoch_mixer: 'MultiModalAttentionEmbedder',
        sequence_mixer: 'SequenceCNN',
        dropout: float = 0.3,
        **kwargs
    ):
        super().__init__()
        _ = kwargs.pop('num_classes', None)

        self.signal_encoders = signal_encoders
        self.epoch_mixer = epoch_mixer
        self.sequence_mixer = sequence_mixer
        self.dropout = nn.Dropout(dropout)

        # 使用优化的AHI回归器
        self.ahi_regressor = OptimalAHIRegressor(
            feature_dim=epoch_mixer.feature_dim,
            dropout=dropout
        )

    @property
    def valid_signals(self) -> list[str]:
        return list(self.signal_encoders.signal_map.keys())

    def forward(self, x: dict[str, Tensor]) -> Tensor:
        z_dict = self.signal_encoders(x)
        z = self.epoch_mixer(z_dict)
        z = self.sequence_mixer(z)
        z = z.mean(dim=1)  # 全局平均池化
        z = self.dropout(z)
        output = self.ahi_regressor(z)  # 使用AHI回归器

        if output.dim() != 1:
            raise ValueError(f"模型输出形状应为[B], 实际得到{output.shape}")
        return output


class SignalEncoder(nn.Module):
    """Enhanced signal encoder with attention and residual connections."""
    def __init__(
        self,
        input_dim: int = 1,
        feature_dim: int = 256,
        activation: str = 'relu',
        frequency: str = 'high',
        size: str = 'large',
        norm: str = 'instance',
        dropout: float = 0.2,
    ):
        super().__init__()
        self.feature_dim = feature_dim
        channels = CHANNEL_CONFIGS[frequency][size]
        self.blocks = nn.ModuleList([
            ConvBlock1D(in_dim, out_dim, activation=activation, norm=norm, dropout=dropout)
            for in_dim, out_dim in zip([input_dim] + channels[:-1], channels)
        ])
        self.epoch_dim = channels[-1] * 4
        
        # Add attention layer
        self.attention = nn.MultiheadAttention(
            embed_dim=channels[-1]*4,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        self.linear = nn.Linear(self.epoch_dim, feature_dim)
        self.activation = get_activation(activation)
        self.dropout = nn.Dropout(dropout)
        
        # 关键修复：添加自适应池化层统一序列长度
        self.adaptive_pool = nn.AdaptiveAvgPool1d(256)

    def forward(self, x: Tensor) -> Tensor:
        B = x.size(0)
        y = x.unsqueeze(1)
        for block in self.blocks:
            y = block(y)

        # 修复reshape问题：确保维度兼容
        y = y.transpose(1, 2)  # [B, C, T] -> [B, T, C]
        B, T, C = y.shape

        # 确保能够正确reshape
        if C != self.epoch_dim:
            # 如果通道数不匹配，使用线性层调整
            if not hasattr(self, 'channel_adjust'):
                self.channel_adjust = nn.Linear(C, self.epoch_dim).to(y.device)
            y = self.channel_adjust(y)

        # Apply attention
        attn_out, _ = self.attention(y, y, y)
        y = y + attn_out  # Residual connection

        # 关键修复：应用自适应池化统一序列长度
        y = y.transpose(1, 2)  # [B, T, C] -> [B, C, T]
        y = self.adaptive_pool(y)  # 统一到256长度
        y = y.transpose(1, 2)  # [B, C, T] -> [B, T, C]

        y = self.activation(self.linear(y))
        return self.dropout(y)

class SignalEncoders(nn.Module):  # 核心编码器容器
    """Multi-signal encoder manager"""
    def __init__(
        self,
        signal_map: dict[str, str],
        feature_dim: int,
        activation: str,
        norm: str = 'instance',
        size: str = 'large',
        dropout: float = 0.2,
    ):
        super().__init__()
        self.feature_dim = feature_dim
        self.signal_map = signal_map
        self.encoders = nn.ModuleDict()
        for signal_name, encoder_name in signal_map.items():
            if encoder_name not in self.encoders:
                frequency = 'high' if COL_MAP[signal_name] == HIGH_FREQ_LEN else 'low'
                self.encoders[encoder_name] = SignalEncoder(
                    input_dim=1,
                    feature_dim=feature_dim,
                    frequency=frequency,
                    activation=activation,
                    size=size,
                    norm=norm,
                    dropout=dropout,
                )

    def forward(self, x: dict[str, Tensor]) -> dict[str, Tensor]:
        return {k: embed_ignore_inf(v, self.get_encoder(k)) for k, v in x.items()}

    def get_encoder(self, signal_name: str) -> nn.Module:
        return self.encoders[self.signal_map[signal_name]]

    def __len__(self) -> int:
        return len(self.encoders)

class MultiModalAttentionEmbedder(nn.Module):
    """Enhanced attention module with deeper transformer."""
    def __init__(
        self,
        feature_dim: int,
        layers: int = 8,  # Increased from 6 to 8
        dropout: float = 0.2,
        dim_ff: int = 2048,  # Increased from 1024
        activation: str = 'gelu',
        norm_first: bool = True,
        nhead: int = 16,  # Increased from 8
        permute_signals: bool = True,
        register_tokens: int = 2,
    ):
        super().__init__()
        self.feature_dim = feature_dim
        self.permute_signals = permute_signals
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=feature_dim,
            dim_feedforward=dim_ff,
            activation=get_activation(activation),
            nhead=nhead,
            batch_first=True,
            dropout=dropout,
            norm_first=norm_first,
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=layers)
        self.register_tokens = nn.Parameter(
            torch.randn(1, 1, feature_dim, register_tokens + 1)
        )
        self.dropout = nn.Dropout(dropout)

    def forward(self, z_dict: dict[str, Tensor]) -> Tensor:
        signals = sorted(z_dict.keys())
        if self.training and self.permute_signals:
            random.shuffle(signals)
        
        z_stack = [z_dict[k] for k in signals]
        z = torch.stack(z_stack, dim=-1)
        
        B, S, F, C = z.shape
        z = torch.cat([
            self.register_tokens.expand(B, S, -1, -1),
            z
        ], dim=-1)
        
        z = z.flatten(0, 1)
        z = self.transformer(z.transpose(1, 2)).transpose(1, 2)
        return self.dropout(z[..., 0].view(B, S, F))

class SequenceCNN(nn.Module):
    """Enhanced sequence mixer with LSTM."""
    def __init__(
        self,
        feature_dim: int = 256,
        dropout: float = 0.2,
        num_layers: int = 4,
        activation: str = 'gelu',
        norm: str = 'batch',
    ):
        super().__init__()
        self.conv_layers = nn.ModuleList([
            DilatedConvBlock(
                feature_dim=feature_dim,
                dropout=dropout,
                activation=activation,
                norm=norm
            ) for _ in range(num_layers)
        ])
        
        # Add bidirectional LSTM
        self.lstm = nn.LSTM(
            input_size=feature_dim,
            hidden_size=feature_dim//2,
            num_layers=2,
            bidirectional=True,
            dropout=dropout,
            batch_first=True
        )
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: Tensor) -> Tensor:
        identity = x
        for layer in self.conv_layers:
            x = layer(x.transpose(1, 2)).transpose(1, 2)
            x = x + identity
            
        # Process through LSTM
        x, _ = self.lstm(x)
        return self.dropout(x)