# 处理大维度时序数据的高效架构分析

## 问题背景
当前数据维度：
- ECG: 387,789 个数据点
- 其他4个信号: 各96,947 个数据点
- 总计: 775,577 个数据点/样本

## 1. 卷积神经网络 (CNN) 架构 ⭐⭐⭐⭐⭐

### 优势
- **线性内存复杂度**: O(n) 而非 O(n²)
- **局部感受野**: 不需要全局注意力计算
- **参数共享**: 卷积核参数与输入长度无关
- **并行计算友好**: GPU优化良好

### 项目中的实现
```python
class SignalEncoder(nn.Module):
    """Enhanced signal encoder with attention and residual connections."""
    def __init__(self, input_dim=1, feature_dim=256, frequency='high'):
        # 多层卷积 + 自适应池化
        self.blocks = nn.ModuleList([ConvBlock1D(...)])
        self.adaptive_pool = nn.AdaptiveAvgPool1d(256)  # 关键：固定输出长度
```

### 显存占用分析
```
输入: [B, 1, 387789] → 24.8MB (batch=8)
Conv1: [B, 32, 387789] → 396MB
Conv2: [B, 64, 193894] → 396MB (stride=2)
Conv3: [B, 128, 96947] → 396MB
最终: [B, 256, 256] → 2MB
峰值显存: ~800MB (可接受)
```

## 2. 分层卷积 + 膨胀卷积 ⭐⭐⭐⭐⭐

### 核心思想
使用不同膨胀率捕获多尺度时序特征

### 项目实现
```python
class DilatedConvBlock(nn.Module):
    DILATIONS = [1, 2, 4, 8, 16]  # 指数增长的膨胀率
    
    def forward(self, x):
        # 每个膨胀卷积捕获不同时间尺度的特征
        for dilation in self.DILATIONS:
            x = conv_layer(x, dilation=dilation)
```

### 优势
- **多尺度特征**: 同时捕获短期和长期依赖
- **参数效率**: 比全连接层参数少得多
- **感受野大**: 指数级增长的感受野

## 3. 分段处理 + 聚合架构 ⭐⭐⭐⭐

### 设计思路
将长序列分成多个段落，分别处理后聚合

```python
class SegmentedProcessor(nn.Module):
    def __init__(self, segment_length=10000):
        self.segment_length = segment_length
        self.segment_encoder = CNN_Encoder()
        self.aggregator = nn.LSTM(hidden_size=256)
    
    def forward(self, x):
        # x: [B, 387789]
        segments = x.unfold(1, self.segment_length, self.segment_length//2)  # 重叠分段
        # segments: [B, num_segments, segment_length]
        
        segment_features = []
        for i in range(segments.size(1)):
            feat = self.segment_encoder(segments[:, i, :])
            segment_features.append(feat)
        
        # 聚合所有段落特征
        aggregated = self.aggregator(torch.stack(segment_features, dim=1))
        return aggregated
```

### 显存优势
```
原始: [B, 387789] → 24.8MB
分段: [B, 38, 10000] → 每次只处理一段 → 峰值显存大幅降低
```

## 4. 递归神经网络 (RNN/LSTM/GRU) ⭐⭐⭐

### 优势
- **序列建模专家**: 天然适合时序数据
- **固定内存**: 隐状态大小固定，与序列长度无关
- **在线处理**: 支持流式处理

### 显存分析
```python
class LSTMProcessor(nn.Module):
    def __init__(self, input_size=1, hidden_size=256):
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
    
    def forward(self, x):
        # x: [B, 387789, 1]
        output, (h_n, c_n) = self.lstm(x)
        return h_n[-1]  # 最后一个隐状态
```

**显存占用**: 仅与hidden_size相关，与序列长度无关！

### 劣势
- **训练慢**: 序列计算，难以并行
- **梯度问题**: 长序列容易梯度消失/爆炸

## 5. 混合架构：CNN + RNN ⭐⭐⭐⭐⭐

### 最佳实践
结合CNN的并行性和RNN的序列建模能力

```python
class HybridProcessor(nn.Module):
    def __init__(self):
        # 第一阶段：CNN降维
        self.cnn_encoder = nn.Sequential(
            nn.Conv1d(1, 64, kernel_size=15, stride=8),  # 387789 → 48473
            nn.Conv1d(64, 128, kernel_size=15, stride=8), # 48473 → 6059
            nn.Conv1d(128, 256, kernel_size=15, stride=4) # 6059 → 1515
        )
        
        # 第二阶段：RNN序列建模
        self.rnn = nn.LSTM(256, 256, num_layers=2, bidirectional=True)
        
        # 第三阶段：注意力聚合
        self.attention = nn.MultiheadAttention(512, num_heads=8)
    
    def forward(self, x):
        # CNN特征提取
        x = self.cnn_encoder(x.unsqueeze(1))  # [B, 256, 1515]
        x = x.transpose(1, 2)  # [B, 1515, 256]
        
        # RNN序列建模
        x, _ = self.rnn(x)  # [B, 1515, 512]
        
        # 注意力聚合
        x, _ = self.attention(x, x, x)
        return x.mean(dim=1)  # 全局平均池化
```

## 6. 项目中的最佳架构：Wav2Sleep ⭐⭐⭐⭐⭐

### 架构分析
```python
class Wav2Sleep(nn.Module):
    def __init__(self, signal_encoders, epoch_mixer, sequence_mixer):
        self.signal_encoders = signal_encoders  # 多信号CNN编码器
        self.epoch_mixer = epoch_mixer          # 多模态注意力
        self.sequence_mixer = sequence_mixer    # 序列CNN
```

### 关键设计
1. **分信号处理**: 每个信号独立编码，避免维度爆炸
2. **自适应池化**: 统一不同信号的序列长度
3. **多模态融合**: 在特征空间而非原始空间融合

### 显存效率
```
ECG编码: [B, 387789] → [B, 256, 256] 
其他编码: [B, 96947] → [B, 256, 256] (×4)
融合: [B, 5×256, 256] → [B, 256]
总显存: < 1GB (batch=8)
```

## 7. 推荐的新架构设计

### 方案A: 多尺度CNN + 时间注意力
```python
class MultiScaleCNN(nn.Module):
    def __init__(self):
        # 多个并行的CNN分支，不同kernel size
        self.branches = nn.ModuleList([
            self._make_branch(kernel_size=15, stride=8),   # 捕获细粒度特征
            self._make_branch(kernel_size=51, stride=32),  # 捕获中等特征  
            self._make_branch(kernel_size=201, stride=128) # 捕获粗粒度特征
        ])
        self.fusion = nn.Conv1d(256*3, 256, 1)
        
    def _make_branch(self, kernel_size, stride):
        return nn.Sequential(
            nn.Conv1d(1, 64, kernel_size, stride),
            nn.Conv1d(64, 128, kernel_size//2, stride//2),
            nn.Conv1d(128, 256, kernel_size//4, stride//4)
        )
```

### 方案B: 分层处理 + 跨层连接
```python
class HierarchicalProcessor(nn.Module):
    def __init__(self):
        # 第一层：粗粒度特征 (大步长)
        self.coarse = nn.Conv1d(1, 128, kernel_size=101, stride=50)
        
        # 第二层：中等粒度特征
        self.medium = nn.Conv1d(1, 128, kernel_size=51, stride=25)
        
        # 第三层：细粒度特征
        self.fine = nn.Conv1d(1, 128, kernel_size=15, stride=5)
        
        # 跨层融合
        self.cross_layer_fusion = CrossLayerAttention()
```

## 8. 实际建议

### 立即可行的方案
1. **使用现有的Wav2Sleep架构** - 已经过优化，显存友好
2. **增强CNN分支** - 添加更多膨胀卷积层
3. **优化自适应池化** - 使用可学习的池化策略

### 长期优化方案
1. **设计专门的多信号融合架构**
2. **引入因果卷积** - 支持实时处理
3. **添加频域分析分支** - FFT + CNN并行处理

### 显存优化技巧
1. **梯度检查点**: `torch.utils.checkpoint`
2. **混合精度**: `torch.cuda.amp`
3. **分批处理**: 将长序列分成多个batch处理
4. **就地操作**: 使用`inplace=True`减少内存分配

## 结论

**最推荐的架构**: 
1. **Wav2Sleep** (现有) - 立即可用，显存友好
2. **多尺度CNN + LSTM** - 最佳性能/显存平衡
3. **分段处理 + 聚合** - 处理超长序列的终极方案

这些架构都能在不压缩原始数据的情况下，通过巧妙的设计实现高效的特征提取和序列建模。
