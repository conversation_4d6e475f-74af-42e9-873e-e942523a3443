"""
AHI回归相关的工具函数和损失函数
专门为优化R²分数设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple


class AdaptiveRegressionLoss(nn.Module):
    """自适应回归损失，专门优化R²"""
    
    def __init__(self, alpha=0.7, beta=0.3, delta=1.0):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.huber = nn.HuberLoss(delta=delta)
        
    def forward(self, pred, target):
        # Huber损失 - 对异常值鲁棒
        huber_loss = self.huber(pred, target)
        
        # R²优化项 - 直接优化相关性
        pred_centered = pred - pred.mean()
        target_centered = target - target.mean()
        
        numerator = torch.sum(pred_centered * target_centered)
        denominator = torch.sqrt(torch.sum(pred_centered**2) * torch.sum(target_centered**2))
        correlation = numerator / (denominator + 1e-8)
        correlation_loss = 1 - correlation  # 最大化相关性
        
        return self.alpha * huber_loss + self.beta * correlation_loss


class WeightedMSELoss(nn.Module):
    """加权MSE损失，对高AHI值给予更高权重"""
    
    def __init__(self, weight_factor=0.1):
        super().__init__()
        self.weight_factor = weight_factor
    
    def forward(self, pred, target):
        # 对高AHI值给予更高权重
        weights = 1 + self.weight_factor * target
        return torch.mean(weights * (pred - target) ** 2)


class AHITrainingOptimizer:
    """专门为AHI回归优化的训练策略"""
    
    def __init__(self, model, learning_rate=1e-4, weight_decay=1e-4):
        self.model = model
        
        # 使用AdamW优化器 + 权重衰减
        self.optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.999)
        )
        
        # 余弦退火学习率调度
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=10,  # 初始周期
            T_mult=2,  # 周期倍增因子
            eta_min=1e-6
        )
        
        # 自适应损失函数
        self.criterion = AdaptiveRegressionLoss()
        
        # 梯度裁剪
        self.max_grad_norm = 1.0
        
    def train_step(self, batch_data, batch_targets):
        self.optimizer.zero_grad()
        
        # 前向传播
        predictions = self.model(batch_data)
        loss = self.criterion(predictions, batch_targets)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(
            self.model.parameters(), 
            self.max_grad_norm
        )
        
        self.optimizer.step()
        self.scheduler.step()
        
        return loss.item(), predictions


def enhance_ahi_data(ahi_values):
    """AHI数据预处理增强"""
    
    # 1. 对数变换 - 处理AHI的偏态分布
    log_ahi = torch.log1p(ahi_values)  # log(1 + x)
    
    # 2. 分位数归一化 - 更好的数值稳定性
    quantiles = torch.quantile(log_ahi, torch.tensor([0.01, 0.99]))
    normalized_ahi = torch.clamp(
        (log_ahi - quantiles[0]) / (quantiles[1] - quantiles[0]),
        0, 1
    )
    
    return normalized_ahi, quantiles


def denormalize_ahi(normalized_ahi, quantiles):
    """反归一化AHI值"""
    log_ahi = normalized_ahi * (quantiles[1] - quantiles[0]) + quantiles[0]
    return torch.expm1(log_ahi)  # exp(x) - 1


def calculate_r2_score(predictions, targets):
    """计算R²分数"""
    ss_res = torch.sum((targets - predictions) ** 2)
    ss_tot = torch.sum((targets - torch.mean(targets)) ** 2)
    r2 = 1 - ss_res / ss_tot
    return r2.item()


def calculate_ahi_metrics(predictions, targets):
    """计算AHI回归评估指标"""
    
    # 基本指标
    mae = torch.mean(torch.abs(predictions - targets))
    mse = torch.mean((predictions - targets) ** 2)
    rmse = torch.sqrt(mse)
    
    # R²分数
    r2 = calculate_r2_score(predictions, targets)
    
    # Pearson相关系数
    pred_centered = predictions - predictions.mean()
    target_centered = targets - targets.mean()
    
    numerator = torch.sum(pred_centered * target_centered)
    denominator = torch.sqrt(torch.sum(pred_centered**2) * torch.sum(target_centered**2))
    pearson = numerator / (denominator + 1e-8)
    
    # MAPE (避免除零)
    mask = targets != 0
    if mask.sum() > 0:
        mape = torch.mean(torch.abs((targets[mask] - predictions[mask]) / targets[mask])) * 100
    else:
        mape = torch.tensor(float('inf'))
    
    return {
        'mae': mae.item(),
        'rmse': rmse.item(),
        'r2': r2,
        'pearson': pearson.item(),
        'mape': mape.item() if mape != float('inf') else float('inf')
    }


def ahi_to_severity_class(ahi_values):
    """将AHI值转换为严重程度分类"""
    # 0: 正常 (AHI < 5)
    # 1: 轻度 (5 <= AHI < 15) 
    # 2: 中度 (15 <= AHI < 30)
    # 3: 重度 (AHI >= 30)
    
    classes = torch.zeros_like(ahi_values, dtype=torch.long)
    classes[(ahi_values >= 5) & (ahi_values < 15)] = 1
    classes[(ahi_values >= 15) & (ahi_values < 30)] = 2
    classes[ahi_values >= 30] = 3
    
    return classes


class R2Tracker:
    """实时R²追踪器"""
    
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.reset()

    def reset(self):
        self.preds = []
        self.targets = []

    def update(self, pred_batch, target_batch):
        """更新预测值和真实值"""
        if isinstance(pred_batch, torch.Tensor):
            pred_batch = pred_batch.detach().cpu().numpy()
        if isinstance(target_batch, torch.Tensor):
            target_batch = target_batch.detach().cpu().numpy()
            
        self.preds.extend(pred_batch)
        self.targets.extend(target_batch)

        # 保持窗口大小
        if len(self.preds) > self.window_size:
            self.preds = self.preds[-self.window_size:]
            self.targets = self.targets[-self.window_size:]

    def get_r2(self):
        """计算当前R²"""
        if len(self.preds) < 2:
            return 0.0
        
        preds = torch.tensor(self.preds)
        targets = torch.tensor(self.targets)
        return calculate_r2_score(preds, targets)

    def get_pearson(self):
        """计算当前Pearson相关系数"""
        if len(self.preds) < 2:
            return 0.0
        
        preds = torch.tensor(self.preds)
        targets = torch.tensor(self.targets)
        
        pred_centered = preds - preds.mean()
        target_centered = targets - targets.mean()
        
        numerator = torch.sum(pred_centered * target_centered)
        denominator = torch.sqrt(torch.sum(pred_centered**2) * torch.sum(target_centered**2))
        
        return (numerator / (denominator + 1e-8)).item()

    def get_stats(self):
        """获取当前统计信息"""
        return {
            'r2': self.get_r2(),
            'pearson': self.get_pearson(),
            'samples': len(self.preds)
        }


def create_ahi_loss_function(loss_type='adaptive'):
    """创建AHI回归损失函数"""
    
    if loss_type == 'adaptive':
        return AdaptiveRegressionLoss(alpha=0.7, beta=0.3)
    elif loss_type == 'huber':
        return nn.HuberLoss(delta=1.0)
    elif loss_type == 'mse':
        return nn.MSELoss()
    elif loss_type == 'mae':
        return nn.L1Loss()
    elif loss_type == 'weighted_mse':
        return WeightedMSELoss(weight_factor=0.1)
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")
